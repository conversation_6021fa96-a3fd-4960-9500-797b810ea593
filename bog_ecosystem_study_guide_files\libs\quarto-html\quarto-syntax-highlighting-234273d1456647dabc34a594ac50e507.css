/* quarto syntax highlight colors */
:root {
  --quarto-hl-al-color: #ff5555;
  --quarto-hl-an-color: #6a737d;
  --quarto-hl-at-color: #d73a49;
  --quarto-hl-bn-color: #005cc5;
  --quarto-hl-bu-color: #d73a49;
  --quarto-hl-ch-color: #032f62;
  --quarto-hl-co-color: #6a737d;
  --quarto-hl-cv-color: #6a737d;
  --quarto-hl-cn-color: #005cc5;
  --quarto-hl-cf-color: #d73a49;
  --quarto-hl-dt-color: #d73a49;
  --quarto-hl-dv-color: #005cc5;
  --quarto-hl-do-color: #6a737d;
  --quarto-hl-er-color: #ff5555;
  --quarto-hl-ex-color: #d73a49;
  --quarto-hl-fl-color: #005cc5;
  --quarto-hl-fu-color: #6f42c1;
  --quarto-hl-im-color: #032f62;
  --quarto-hl-in-color: #6a737d;
  --quarto-hl-kw-color: #d73a49;
  --quarto-hl-op-color: #24292e;
  --quarto-hl-pp-color: #d73a49;
  --quarto-hl-re-color: #6a737d;
  --quarto-hl-sc-color: #005cc5;
  --quarto-hl-ss-color: #032f62;
  --quarto-hl-st-color: #032f62;
  --quarto-hl-va-color: #e36209;
  --quarto-hl-vs-color: #032f62;
  --quarto-hl-wa-color: #ff5555;
}

/* other quarto variables */
:root {
  --quarto-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/* syntax highlight based on Pandoc's rules */
pre > code.sourceCode > span {
  color: #24292e;
}

code.sourceCode > span {
  color: #24292e;
}

div.sourceCode,
div.sourceCode pre.sourceCode {
  color: #24292e;
}

/* Normal */
code span {
  color: #24292e;
}

/* Alert */
code span.al {
  font-weight: bold;
  color: #ff5555;
}

/* Annotation */
code span.an {
  color: #6a737d;
}

/* Attribute */
code span.at {
  color: #d73a49;
}

/* BaseN */
code span.bn {
  color: #005cc5;
}

/* BuiltIn */
code span.bu {
  color: #d73a49;
}

/* ControlFlow */
code span.cf {
  color: #d73a49;
}

/* Char */
code span.ch {
  color: #032f62;
}

/* Constant */
code span.cn {
  color: #005cc5;
}

/* Comment */
code span.co {
  color: #6a737d;
}

/* CommentVar */
code span.cv {
  color: #6a737d;
}

/* Documentation */
code span.do {
  color: #6a737d;
}

/* DataType */
code span.dt {
  color: #d73a49;
}

/* DecVal */
code span.dv {
  color: #005cc5;
}

/* Error */
code span.er {
  color: #ff5555;
  text-decoration: underline;
}

/* Extension */
code span.ex {
  font-weight: bold;
  color: #d73a49;
}

/* Float */
code span.fl {
  color: #005cc5;
}

/* Function */
code span.fu {
  color: #6f42c1;
}

/* Import */
code span.im {
  color: #032f62;
}

/* Information */
code span.in {
  color: #6a737d;
}

/* Keyword */
code span.kw {
  color: #d73a49;
}

/* Operator */
code span.op {
  color: #24292e;
}

/* Preprocessor */
code span.pp {
  color: #d73a49;
}

/* RegionMarker */
code span.re {
  color: #6a737d;
}

/* SpecialChar */
code span.sc {
  color: #005cc5;
}

/* SpecialString */
code span.ss {
  color: #032f62;
}

/* String */
code span.st {
  color: #032f62;
}

/* Variable */
code span.va {
  color: #e36209;
}

/* VerbatimString */
code span.vs {
  color: #032f62;
}

/* Warning */
code span.wa {
  color: #ff5555;
}

.prevent-inlining {
  content: "</";
}

/*# sourceMappingURL=c56d45e8171e82081af6d43de8e882ad.css.map */
