<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>Information: Natur</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="22b869c6-582f-80f5-87e2-e75c9d110fd6" class="page sans"><header><h1 class="page-title">Information: Natur</h1><p class="page-description"></p><table class="properties"><tbody><tr class="property-row property-row-multi_select"><th><span class="icon property-icon"><img src="https://www.notion.so/icons/tag_gray.svg" style="width:14px;height:14px;display:block"/></span>Tags</th><td></td></tr><tr class="property-row property-row-status"><th><span class="icon property-icon"><svg aria-hidden="true" role="graphics-symbol" viewBox="0 0 16 16" style="width:14px;height:14px;display:block;fill:rgba(71, 70, 68, 0.6);flex-shrink:0" class="typesStatus"><path d="M8.75488 1.02344C8.75488 0.613281 8.41309 0.264648 8.00293 0.264648C7.59277 0.264648 7.25098 0.613281 7.25098 1.02344V3.11523C7.25098 3.51855 7.59277 3.86719 8.00293 3.86719C8.41309 3.86719 8.75488 3.51855 8.75488 3.11523V1.02344ZM3.91504 5.0293C4.20215 5.31641 4.69434 5.32324 4.97461 5.03613C5.26855 4.74902 5.26855 4.25684 4.98145 3.96973L3.53906 2.52051C3.25195 2.2334 2.7666 2.21973 2.47949 2.50684C2.19238 2.79395 2.18555 3.28613 2.47266 3.57324L3.91504 5.0293ZM10.9629 4.01758C10.6826 4.30469 10.6826 4.79688 10.9697 5.08398C11.2568 5.37109 11.749 5.36426 12.0361 5.07715L13.4854 3.62793C13.7725 3.34082 13.7725 2.84863 13.4785 2.55469C13.1982 2.27441 12.7061 2.27441 12.4189 2.56152L10.9629 4.01758ZM15.0234 8.78906C15.4336 8.78906 15.7822 8.44727 15.7822 8.03711C15.7822 7.62695 15.4336 7.28516 15.0234 7.28516H12.9385C12.5283 7.28516 12.1797 7.62695 12.1797 8.03711C12.1797 8.44727 12.5283 8.78906 12.9385 8.78906H15.0234ZM0.975586 7.28516C0.56543 7.28516 0.223633 7.62695 0.223633 8.03711C0.223633 8.44727 0.56543 8.78906 0.975586 8.78906H3.07422C3.48438 8.78906 3.83301 8.44727 3.83301 8.03711C3.83301 7.62695 3.48438 7.28516 3.07422 7.28516H0.975586ZM12.0361 10.9902C11.749 10.71 11.2568 10.71 10.9629 10.9971C10.6826 11.2842 10.6826 11.7764 10.9697 12.0635L12.4258 13.5127C12.7129 13.7998 13.2051 13.793 13.4922 13.5059C13.7793 13.2256 13.7725 12.7266 13.4854 12.4395L12.0361 10.9902ZM2.52051 12.4395C2.22656 12.7266 2.22656 13.2188 2.50684 13.5059C2.79395 13.793 3.28613 13.7998 3.57324 13.5127L5.02246 12.0703C5.31641 11.7832 5.31641 11.291 5.03613 11.0039C4.74902 10.7168 4.25684 10.71 3.96973 10.9971L2.52051 12.4395ZM8.75488 12.9658C8.75488 12.5557 8.41309 12.207 8.00293 12.207C7.59277 12.207 7.25098 12.5557 7.25098 12.9658V15.0576C7.25098 15.4609 7.59277 15.8096 8.00293 15.8096C8.41309 15.8096 8.75488 15.4609 8.75488 15.0576V12.9658Z"></path></svg></span>Priority</th><td><span class="status-value select-value-color-green"><div class="status-dot status-dot-color-green"></div>Medium</span></td></tr><tr class="property-row property-row-checkbox"><th><span class="icon property-icon"><img src="https://www.notion.so/icons/delete_gray.svg" style="width:14px;height:14px;display:block"/></span>Archive</th><td><div class="checkbox checkbox-off"></div></td></tr><tr class="property-row property-row-relation"><th><span class="icon property-icon"><svg aria-hidden="true" role="graphics-symbol" viewBox="0 0 16 16" style="width:14px;height:14px;display:block;fill:rgba(71, 70, 68, 0.6);flex-shrink:0" class="typesRelation"><path d="M13.1475 10.5869V3.72363C13.1475 3.25195 12.833 2.93066 12.3477 2.93066H5.48438C5.02637 2.93066 4.70508 3.27246 4.70508 3.67578C4.70508 4.07227 5.05371 4.40039 5.46387 4.40039H7.89746L10.8438 4.30469L9.59961 5.39844L3.08496 11.9199C2.92773 12.0771 2.8457 12.2686 2.8457 12.46C2.8457 12.8564 3.20801 13.2256 3.61816 13.2256C3.80957 13.2256 3.99414 13.1504 4.15137 12.9932L10.6729 6.47168L11.7803 5.22754L11.6641 8.05762V10.6074C11.6641 11.0176 11.9922 11.373 12.4023 11.373C12.8057 11.373 13.1475 11.0312 13.1475 10.5869Z"></path></svg></span>Areas/Resources</th><td><a href="https://www.notion.so/AVJNF-193869c6582f80a0aac3d0fcfcbd3641?pvs=21"><img class="icon" src="https://www.notion.so/icons/feather_green.svg"/>AVJNF</a></td></tr><tr class="property-row property-row-date"><th><span class="icon property-icon"><svg aria-hidden="true" role="graphics-symbol" viewBox="0 0 16 16" style="width:14px;height:14px;display:block;fill:rgba(71, 70, 68, 0.6);flex-shrink:0" class="typesDate"><path d="M3.29688 14.4561H12.7031C14.1797 14.4561 14.9453 13.6904 14.9453 12.2344V3.91504C14.9453 2.45215 14.1797 1.69336 12.7031 1.69336H3.29688C1.82031 1.69336 1.05469 2.45215 1.05469 3.91504V12.2344C1.05469 13.6973 1.82031 14.4561 3.29688 14.4561ZM3.27637 13.1162C2.70898 13.1162 2.39453 12.8154 2.39453 12.2207V5.9043C2.39453 5.30273 2.70898 5.00879 3.27637 5.00879H12.71C13.2842 5.00879 13.6055 5.30273 13.6055 5.9043V12.2207C13.6055 12.8154 13.2842 13.1162 12.71 13.1162H3.27637ZM6.68066 7.38086H7.08398C7.33008 7.38086 7.41211 7.30566 7.41211 7.05957V6.66309C7.41211 6.41699 7.33008 6.3418 7.08398 6.3418H6.68066C6.44141 6.3418 6.35938 6.41699 6.35938 6.66309V7.05957C6.35938 7.30566 6.44141 7.38086 6.68066 7.38086ZM8.92285 7.38086H9.31934C9.56543 7.38086 9.64746 7.30566 9.64746 7.05957V6.66309C9.64746 6.41699 9.56543 6.3418 9.31934 6.3418H8.92285C8.67676 6.3418 8.59473 6.41699 8.59473 6.66309V7.05957C8.59473 7.30566 8.67676 7.38086 8.92285 7.38086ZM11.1582 7.38086H11.5547C11.8008 7.38086 11.8828 7.30566 11.8828 7.05957V6.66309C11.8828 6.41699 11.8008 6.3418 11.5547 6.3418H11.1582C10.9121 6.3418 10.8301 6.41699 10.8301 6.66309V7.05957C10.8301 7.30566 10.9121 7.38086 11.1582 7.38086ZM4.44531 9.58203H4.84863C5.09473 9.58203 5.17676 9.50684 5.17676 9.26074V8.86426C5.17676 8.61816 5.09473 8.54297 4.84863 8.54297H4.44531C4.20605 8.54297 4.12402 8.61816 4.12402 8.86426V9.26074C4.12402 9.50684 4.20605 9.58203 4.44531 9.58203ZM6.68066 9.58203H7.08398C7.33008 9.58203 7.41211 9.50684 7.41211 9.26074V8.86426C7.41211 8.61816 7.33008 8.54297 7.08398 8.54297H6.68066C6.44141 8.54297 6.35938 8.61816 6.35938 8.86426V9.26074C6.35938 9.50684 6.44141 9.58203 6.68066 9.58203ZM8.92285 9.58203H9.31934C9.56543 9.58203 9.64746 9.50684 9.64746 9.26074V8.86426C9.64746 8.61816 9.56543 8.54297 9.31934 8.54297H8.92285C8.67676 8.54297 8.59473 8.61816 8.59473 8.86426V9.26074C8.59473 9.50684 8.67676 9.58203 8.92285 9.58203ZM11.1582 9.58203H11.5547C11.8008 9.58203 11.8828 9.50684 11.8828 9.26074V8.86426C11.8828 8.61816 11.8008 8.54297 11.5547 8.54297H11.1582C10.9121 8.54297 10.8301 8.61816 10.8301 8.86426V9.26074C10.8301 9.50684 10.9121 9.58203 11.1582 9.58203ZM4.44531 11.7832H4.84863C5.09473 11.7832 5.17676 11.708 5.17676 11.4619V11.0654C5.17676 10.8193 5.09473 10.7441 4.84863 10.7441H4.44531C4.20605 10.7441 4.12402 10.8193 4.12402 11.0654V11.4619C4.12402 11.708 4.20605 11.7832 4.44531 11.7832ZM6.68066 11.7832H7.08398C7.33008 11.7832 7.41211 11.708 7.41211 11.4619V11.0654C7.41211 10.8193 7.33008 10.7441 7.08398 10.7441H6.68066C6.44141 10.7441 6.35938 10.8193 6.35938 11.0654V11.4619C6.35938 11.708 6.44141 11.7832 6.68066 11.7832ZM8.92285 11.7832H9.31934C9.56543 11.7832 9.64746 11.708 9.64746 11.4619V11.0654C9.64746 10.8193 9.56543 10.7441 9.31934 10.7441H8.92285C8.67676 10.7441 8.59473 10.8193 8.59473 11.0654V11.4619C8.59473 11.708 8.67676 11.7832 8.92285 11.7832Z"></path></svg></span>Due</th><td><time>@July 9, 2025</time></td></tr><tr class="property-row property-row-relation"><th><span class="icon property-icon"><svg aria-hidden="true" role="graphics-symbol" viewBox="0 0 16 16" style="width:14px;height:14px;display:block;fill:rgba(71, 70, 68, 0.6);flex-shrink:0" class="typesRelation"><path d="M13.1475 10.5869V3.72363C13.1475 3.25195 12.833 2.93066 12.3477 2.93066H5.48438C5.02637 2.93066 4.70508 3.27246 4.70508 3.67578C4.70508 4.07227 5.05371 4.40039 5.46387 4.40039H7.89746L10.8438 4.30469L9.59961 5.39844L3.08496 11.9199C2.92773 12.0771 2.8457 12.2686 2.8457 12.46C2.8457 12.8564 3.20801 13.2256 3.61816 13.2256C3.80957 13.2256 3.99414 13.1504 4.15137 12.9932L10.6729 6.47168L11.7803 5.22754L11.6641 8.05762V10.6074C11.6641 11.0176 11.9922 11.373 12.4023 11.373C12.8057 11.373 13.1475 11.0312 13.1475 10.5869Z"></path></svg></span>Project</th><td><a href="https://www.notion.so/Aaby-Mose-22b869c6582f80838638e8bbbef6dab8?pvs=21"><img class="icon" src="https://www.notion.so/icons/tree_green.svg"/>Aaby Mose</a></td></tr><tr class="property-row property-row-rollup"><th><span class="icon property-icon"><svg aria-hidden="true" role="graphics-symbol" viewBox="0 0 16 16" style="width:14px;height:14px;display:block;fill:rgba(71, 70, 68, 0.6);flex-shrink:0" class="typesRollup"><path d="M1.25293 6.82031C1.25293 9.88965 3.74805 12.3848 6.81738 12.3848C7.95898 12.3848 9.00488 12.0361 9.87988 11.4414L13.0244 14.5859C13.2158 14.7842 13.4824 14.873 13.749 14.873C14.3301 14.873 14.7471 14.4355 14.7471 13.8682C14.7471 13.5947 14.6514 13.3418 14.4668 13.1504L11.3428 10.0195C11.9922 9.12402 12.3818 8.0166 12.3818 6.82031C12.3818 3.75098 9.88672 1.25586 6.81738 1.25586C3.74805 1.25586 1.25293 3.75098 1.25293 6.82031ZM2.70215 6.82031C2.70215 4.55078 4.54102 2.70508 6.81738 2.70508C9.08691 2.70508 10.9326 4.55078 10.9326 6.82031C10.9326 9.08984 9.08691 10.9355 6.81738 10.9355C4.54102 10.9355 2.70215 9.08984 2.70215 6.82031Z"></path></svg></span>Root Area</th><td>AVJNF</td></tr><tr class="property-row property-row-status"><th><span class="icon property-icon"><svg aria-hidden="true" role="graphics-symbol" viewBox="0 0 16 16" style="width:14px;height:14px;display:block;fill:rgba(71, 70, 68, 0.6);flex-shrink:0" class="typesStatus"><path d="M8.75488 1.02344C8.75488 0.613281 8.41309 0.264648 8.00293 0.264648C7.59277 0.264648 7.25098 0.613281 7.25098 1.02344V3.11523C7.25098 3.51855 7.59277 3.86719 8.00293 3.86719C8.41309 3.86719 8.75488 3.51855 8.75488 3.11523V1.02344ZM3.91504 5.0293C4.20215 5.31641 4.69434 5.32324 4.97461 5.03613C5.26855 4.74902 5.26855 4.25684 4.98145 3.96973L3.53906 2.52051C3.25195 2.2334 2.7666 2.21973 2.47949 2.50684C2.19238 2.79395 2.18555 3.28613 2.47266 3.57324L3.91504 5.0293ZM10.9629 4.01758C10.6826 4.30469 10.6826 4.79688 10.9697 5.08398C11.2568 5.37109 11.749 5.36426 12.0361 5.07715L13.4854 3.62793C13.7725 3.34082 13.7725 2.84863 13.4785 2.55469C13.1982 2.27441 12.7061 2.27441 12.4189 2.56152L10.9629 4.01758ZM15.0234 8.78906C15.4336 8.78906 15.7822 8.44727 15.7822 8.03711C15.7822 7.62695 15.4336 7.28516 15.0234 7.28516H12.9385C12.5283 7.28516 12.1797 7.62695 12.1797 8.03711C12.1797 8.44727 12.5283 8.78906 12.9385 8.78906H15.0234ZM0.975586 7.28516C0.56543 7.28516 0.223633 7.62695 0.223633 8.03711C0.223633 8.44727 0.56543 8.78906 0.975586 8.78906H3.07422C3.48438 8.78906 3.83301 8.44727 3.83301 8.03711C3.83301 7.62695 3.48438 7.28516 3.07422 7.28516H0.975586ZM12.0361 10.9902C11.749 10.71 11.2568 10.71 10.9629 10.9971C10.6826 11.2842 10.6826 11.7764 10.9697 12.0635L12.4258 13.5127C12.7129 13.7998 13.2051 13.793 13.4922 13.5059C13.7793 13.2256 13.7725 12.7266 13.4854 12.4395L12.0361 10.9902ZM2.52051 12.4395C2.22656 12.7266 2.22656 13.2188 2.50684 13.5059C2.79395 13.793 3.28613 13.7998 3.57324 13.5127L5.02246 12.0703C5.31641 11.7832 5.31641 11.291 5.03613 11.0039C4.74902 10.7168 4.25684 10.71 3.96973 10.9971L2.52051 12.4395ZM8.75488 12.9658C8.75488 12.5557 8.41309 12.207 8.00293 12.207C7.59277 12.207 7.25098 12.5557 7.25098 12.9658V15.0576C7.25098 15.4609 7.59277 15.8096 8.00293 15.8096C8.41309 15.8096 8.75488 15.4609 8.75488 15.0576V12.9658Z"></path></svg></span>Status</th><td><span class="status-value select-value-color-blue"><div class="status-dot status-dot-color-blue"></div>In progress</span></td></tr></tbody></table></header><div class="page-body"><ul id="22b869c6-582f-804c-bb33-f4139ffb1254" class="block-color-purple bulleted-list"><li style="list-style-type:disc"><strong>Area (purple)</strong>: Identifies habitat types, classifications, and spatial characteristics of bog ecosystems</li></ul><ul id="22b869c6-582f-8013-9cd4-d09beed8a1b8" class="block-color-blue bulleted-list"><li style="list-style-type:disc"><strong>Management (blue)</strong>: Highlights water conditions, hydrology, and physical characteristics of bogs</li></ul><ul id="22b869c6-582f-802a-9b6f-f37eaed9a62d" class="block-color-teal bulleted-list"><li style="list-style-type:disc"><strong>Conservation (green)</strong>: Marks biodiversity significance, protection status, and ecological importance</li></ul><ul id="22b869c6-582f-80d1-b206-d76021d7d0d0" class="block-color-orange bulleted-list"><li style="list-style-type:disc"><strong>Restoration &amp; Wildlife (orange)</strong>: Indicates threats, disturbances, and management challenges</li></ul><ul id="22b869c6-582f-80fb-ada1-ce33f3bceead" class="block-color-red bulleted-list"><li style="list-style-type:disc"><strong>Rare Plants (red)</strong>: Identifies key plant species characteristic of bog ecosystems</li></ul><ul id="22b869c6-582f-8053-8406-dea5556b9c16" class="block-color-yellow bulleted-list"><li style="list-style-type:disc"><strong>History (yellow)</strong>: Focuses on formation processes, development, and natural dynamics</li></ul><h2 id="22b869c6-582f-80f3-95bf-ce67224a6e52" class="">Bogs</h2><p id="22b869c6-582f-806c-9d61-d9a333acac1a" class="">Bogs occur naturally in areas with <mark class="highlight-blue"><strong>high water levels</strong></mark> and contain a wide range of plant communities, <mark class="highlight-teal"><strong>seven of which are covered by the Habitats Directive</strong></mark>. <mark class="highlight-purple"><strong>Transition mires (7140)</strong></mark> are a variable habitat type that begins by forming a floating plant cover in water, along lakes and springs, or in depressions in fens and heaths. As <mark class="highlight-red"><strong>Sphagnum species</strong></mark> colonize, the peat layer can gradually lose contact with groundwater, and the extremely nutrient-poor and acidic bog type, <mark class="highlight-purple"><strong>active raised bog (7110)</strong></mark>, develops where <mark class="highlight-blue"><strong>water and nutrient supply comes solely from precipitation</strong></mark>. With <mark class="highlight-orange"><strong>drainage and/or nutrient loading</strong></mark>, the active raised bog develops into a degraded version, which in the Habitats Directive is considered a separate habitat type, <mark class="highlight-purple"><strong>degraded raised bog (7120)</strong></mark>. <mark class="highlight-orange"><strong>Peat cutting, trampling, or natural dynamics</strong></mark> can give rise to the rare habitat type, <mark class="highlight-purple"><strong>bare peat in raised bogs (7150)</strong></mark>, which occurs naturally as pioneer vegetation on exposed peat in raised bogs and heathland bogs. <mark class="highlight-yellow"><strong>Natural dynamics include frost and water-eroded areas or wet and occasionally flooded sandy areas</strong></mark> in damp heaths and bogs, which can develop into bare peat depressions. On calcareous, moist soil with high groundwater levels, the <mark class="highlight-teal"><strong>species-rich habitat type rich fen (7230)</strong></mark> develops. When the impressive sedge, <mark class="highlight-red"><strong>saw sedge</strong></mark>, dominates, the habitat type is classified as <mark class="highlight-purple"><strong>Cladium mariscus beds (7210)</strong></mark>. <mark class="highlight-blue"><strong>Petrifying springs (7220)</strong></mark> are found where emerging calcareous groundwater forms freely flowing water for most of the year.</p><hr id="22b869c6-582f-8081-9e3e-f72bb48ec103"/><h3 id="22b869c6-582f-8036-8cda-eff144c4ed5b" class="">Active raised bog (7110)</h3><p id="22b869c6-582f-808e-b4c1-e8a6749ce022" class="">Raised bogs are characterized by having built up so much <mark class="highlight-blue"><strong>peat that the bog has no connection with the groundwater</strong></mark> in the underlying soil and therefore <mark class="highlight-blue"><strong>only receives rainwater</strong></mark>. The peat layer maintains a so-called <mark class="highlight-blue"><strong>&#x27;secondary water table&#x27;</strong></mark>, and the raised bog is <mark class="highlight-blue"><strong>calcium-poor, acidic, and naturally nutrient-poor</strong></mark>. A raised bog can schematically be divided into <mark class="highlight-purple"><strong>three distinct units</strong></mark>, all of which are covered by the habitat type as long as the bog is active and the area is not forested: the <mark class="highlight-purple"><strong>raised bog surface, the rand zone, and the lagg zone</strong></mark>. Only a few species of <mark class="highlight-red"><strong>vascular plants and mosses</strong></mark> are specialized to thrive in the <mark class="highlight-blue"><strong>extremely nutrient-poor, acidic, and wet environment</strong></mark> of the surface. The open central raised bog surface is dominated by <mark class="highlight-red"><strong>peat mosses and dwarf shrubs</strong></mark>, and is the <mark class="highlight-teal"><strong>only Danish terrestrial habitat type that does not contain grass species</strong></mark>. The term <mark class="highlight-yellow"><strong>&#x27;active&#x27; refers to the ongoing active peat formation</strong></mark> on the raised bog surface.</p><h3 id="22b869c6-582f-80c6-8da1-cd9d4628ecd1" class="">Degraded raised bog (7120)</h3><p id="22b869c6-582f-80c2-97e6-e606914f7bf5" class="">Raised bog sections that have had their <mark class="highlight-orange"><strong>natural water balance significantly disturbed</strong></mark>, but where <mark class="highlight-teal"><strong>open raised bog vegetation still remains</strong></mark>. However, the <mark class="highlight-red"><strong>raised bog plants have changed in frequency and distribution</strong></mark>, including the <mark class="highlight-orange"><strong>decline or disappearance of peat moss</strong></mark>, and instead an <mark class="highlight-orange"><strong>invasion of purple moor-grass and trees</strong></mark> is seen on the raised bog surface. The majority of species will often be the same as in the active raised bog.</p><h3 id="22b869c6-582f-803c-9bce-cd1a5c5e6553" class="">Transition mires (7140)</h3><p id="22b869c6-582f-8008-a489-f39552d7e68a" class="">The common feature of this habitat type is that it <mark class="highlight-yellow"><strong>floats in water or originally started floating in water</strong></mark>. Transition mires are most often formed at the <mark class="highlight-purple"><strong>edges of lakes and ponds, including peat excavations</strong></mark>, but can also be found in <mark class="highlight-purple"><strong>calm stream sections, in connection with springs, or in depressions in fens and heaths</strong></mark>. <mark class="highlight-red"><strong>Mosses often constitute a significant part of the vegetation</strong></mark>, and in late succession stages, <mark class="highlight-orange"><strong>shrubs and trees invade</strong></mark>. When the vegetation changes to <mark class="highlight-orange"><strong>forest or scrub (&gt; 50% coverage of woody plants)</strong></mark>, it is no longer this habitat type, while the other succession stages belong to the type.</p><h3 id="22b869c6-582f-808f-a1b9-d6734eb16d12" class="">Bog woodland (91D0)</h3><p id="22b869c6-582f-80d7-8c88-d762cd78146d" class="">Bog woodland is dominated by <mark class="highlight-red"><strong>birch, Scots pine or Norway spruce</strong></mark> and occurs on <mark class="highlight-blue"><strong>relatively nutrient-poor, acidic soil with a high water table</strong></mark>. <mark class="highlight-yellow"><strong>Birch is often the first species in the succession</strong></mark>, e.g., when transition mires, heathland bogs, or poor fens become overgrown.</p><details open=""><summary style="font-weight:600;font-size:1.25em;line-height:1.3;margin:0">Original</summary><div class="indented"><h2 id="22b869c6-582f-80da-ada5-d6dbe2bef805" class="">Moser</h2><p id="22b869c6-582f-80a5-b9e7-dcf5d3deff42" class="">Moser findes naturligt på arealer med en høj vandstand og rummer en lang række plantesamfund, hvoraf syv er omfattet af habitatdirektivet. Hængesæk (7140) er en variabel naturtype, der starter ved at danne et flydende plantedække i vand, langs søer og kildevæld eller i lavninger i kær og heder. Ved tilgroning med især sphagnumarter kan tørvelaget gradvist miste kontakt til grundvandet, og den ekstremt næringsfattige og sure mosetype, aktiv højmose (7110), udvikles, hvor vand- og næringstilførslen alene kommer via nedbøren. Ved afvanding og/eller næringsbelastning udvikler den aktive højmose sig til en degenereret udgave, der i Habitatdirektivet opfattes som en selvstændig naturtype, nedbrudt højmose (7120). Tørveskrælning, optrædning eller naturlig dynamik kan give anledning til den sjældne naturtype tørvelavning (7150), der findes naturligt som pionervegetation på blottet tørv i højmoser og hedemoser. Naturlig dynamik omfatter fx frost- og vanderoderede partier eller våde og tidvis oversvømmede sandflader i fugtige heder og moser, der kan udvikle sig til tørvelavninger. På kalkrig, fugtig bund med høj grundvandstand udvikles den artsrige naturtype rigkær (7230). Dominerer den anselige halvgræs, hvas avneknippe, henføres naturtypen til avneknippemose (7210). Kildevæld (7220) findes, hvor fremvældende kalkrigt grundvand danner frit rindende vand i hovedparten af året.</p><hr id="22b869c6-582f-80c3-86cb-eb95d10ef5c8"/><h3 id="22b869c6-582f-8089-962b-e35b09f9e7cb" class="">Aktiv højmose (7110)</h3><p id="22b869c6-582f-809b-b871-e7d3a157f896" class="">Højmoser er kendetegnet ved, at der er opbygget så meget tørv, at mosen ikke har forbindelse med grundvandet i den underliggende jordbund og derfor kun modtager regnvand. Tørvelaget opretholder et såkaldt ’sekundært vandspejl’, og højmosen er kalkfattig, sur og naturligt næringsfattig. En højmose kan skematisk opdeles i tre særskilte enheder, som alle er omfattet af naturtypen, så længe mosen er aktiv og arealet ikke skovbevokset: højmosefladen, randen og laggen. Kun få arter af karplanter og mosser er specialiserede til at trives i fladens ekstremt næringsfattige, sure og våde miljø. Den åbne centrale højmoseflade er domineret af tørvemosser og dværgbuske, og er den eneste danske terrestriske naturtype, som ikke indeholder græsarter. Betegnelsen &#x27;aktiv&#x27; henviser til, at der skal foregå en aktiv tørveopbygning på højmosefladen.</p><h3 id="22b869c6-582f-800a-8fa5-e5ac7ba4f277" class="">Nedbrudt højmose (7120)</h3><p id="22b869c6-582f-80e8-bbe8-fb8ad7b7197c" class="">Højmosepartier, som væsentligt har fået forstyrret deres naturlige vandbalance, men hvor der fortsat er lysåben højmosevegetation. Højmoseplanterne har dog ændret hyppighed og fordeling, bl.a. med tilbagegang eller forsvinden af tørvemos og i stedet ses invasion af blåtop og træer på højmosefladen. Hovedparten af arterne vil ofte være de samme som i den aktive højmose.</p><h3 id="22b869c6-582f-8057-ba9e-c8d3beb788b5" class="">Hængesæk (7140)</h3><p id="22b869c6-582f-80ea-9d38-d2fcb15071e3" class="">Naturtypens fællestræk er, at den flyder i vand eller oprindelig er startet flydende i vand. Hængesæk dannes oftest ved kanten af søer og vandhuller, herunder tørvegrave, men kan også findes i rolige vandløbsafsnit, i forbindelse med kildevæld, eller i lavninger i kær og hede. Mosser udgør ofte en væsentlig del af vegetationen, og i sene successionsstadier indvandrer buske og træer. Når  vegetationen skifter til skov eller krat (&gt; 50 % dækning af vedplanter) er det ikke længere denne naturtype, mens de andre successionsstadier hører med til typen.</p><h3 id="22b869c6-582f-802b-91e0-d9bf05c5bfdc" class="">Skovbevokset tørvemose (91D0)</h3><p id="22b869c6-582f-80ed-a550-d3b1ad95afd9" class="">Skovbevokset tørvemose er domineret af birk, skovfyr eller rødgran og forekommer på relativ næringsfattig, sur bund med højt grundvandsspejl. Ofte er birk første art i successionen, fx ved tilgroning af hængesæk, hedemoser eller fattigkær.</p></div></details></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>